import pandas as pd

# 원본 데이터에서 5주차 데이터 확인
print("📊 원본 inventory.xlsx에서 5주차 데이터 확인")
print("=" * 50)

df = pd.read_excel('inventory.xlsx', header=0)

# 5주차 컬럼 확인
print(f"5주차 컬럼명: '{df.columns[24]}'")  # 25번째 컬럼이 5주차
print(f"5주차 데이터 타입: {df.dtypes[df.columns[24]]}")

# 5주차 데이터가 있는 행들 확인
five_week_col = df.columns[24]  # '5주차'
print(f"\n5주차 데이터 분석:")
print(f"  - 전체 행 수: {len(df)}")
print(f"  - 5주차 데이터가 있는 행 수: {len(df[df[five_week_col].notna()])}")
print(f"  - 5주차 데이터가 0보다 큰 행 수: {len(df[df[five_week_col] > 0])}")

# 5주차 데이터가 있는 행들의 샘플
five_week_data = df[df[five_week_col] > 0]
if len(five_week_data) > 0:
    print(f"\n5주차 데이터 샘플 (처음 5행):")
    week_cols = ['1주차', '2주차', '3주차', '4주차', '5주차']
    print(five_week_data[week_cols].head())
    
    # 5주차까지 모든 데이터가 있는 행 찾기
    all_weeks_data = five_week_data
    for col in week_cols:
        all_weeks_data = all_weeks_data[all_weeks_data[col] > 0]
    
    print(f"\n1주차~5주차 모든 데이터가 있는 행 수: {len(all_weeks_data)}")
    if len(all_weeks_data) > 0:
        print("샘플:")
        print(all_weeks_data[week_cols].head())
else:
    print("\n❌ 5주차 데이터가 0보다 큰 행이 없습니다.")

# 각 주차별 데이터 분포 확인
print(f"\n📊 각 주차별 데이터 분포:")
week_cols = ['1주차', '2주차', '3주차', '4주차', '5주차']
for col in week_cols:
    if col in df.columns:
        non_zero_count = len(df[df[col] > 0])
        print(f"  - {col}: {non_zero_count}개 행에 데이터 있음")
    else:
        print(f"  - {col}: 컬럼 없음")
