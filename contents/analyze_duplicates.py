import pandas as pd
import numpy as np

def analyze_h_column_duplicates():
    """
    11.xlsx 파일의 H열(구성품) 중복값을 분석하고 
    cate4, cate5, 상품명과 매칭하여 결과 파일을 생성합니다.
    """
    
    try:
        # Excel 파일 읽기
        print("Excel 파일을 읽는 중...")
        df = pd.read_excel('11.xlsx')
        
        # 첫 번째 행을 헤더로 설정
        df.columns = df.iloc[0]  # 첫 번째 행을 컬럼명으로 설정
        df = df.drop(df.index[0])  # 첫 번째 행 제거
        df = df.reset_index(drop=True)
        
        print(f"데이터 형태: {df.shape}")
        print("컬럼명:", df.columns.tolist())
        
        # H열 찾기 (8번째 컬럼이 구성품 컬럼)
        h_column = df.columns[7]  # 8번째 컬럼 (0부터 시작하므로 인덱스 7)
        cate4_column = df.columns[2]  # Cate4
        cate5_column = df.columns[3]  # Cate5
        product_name_column = df.columns[5]  # 상품명
        
        print(f"\n분석 대상 컬럼:")
        print(f"H열 (구성품): {h_column}")
        print(f"Cate4: {cate4_column}")
        print(f"Cate5: {cate5_column}")
        print(f"상품명: {product_name_column}")
        
        # H열의 중복값 계산
        print(f"\nH열 중복값 분석 중...")
        
        # NaN 값 제거
        df_clean = df.dropna(subset=[h_column])
        
        # H열 값별 중복 개수 계산
        h_value_counts = df_clean[h_column].value_counts()
        
        print(f"H열 총 고유값 개수: {len(h_value_counts)}")
        print(f"H열 중복값이 있는 항목 개수: {len(h_value_counts[h_value_counts > 1])}")
        
        # 중복값이 있는 항목들과 관련 정보 추출
        duplicate_analysis = []
        
        for h_value, count in h_value_counts.items():
            if count > 1:  # 중복값만 처리
                # 해당 H값을 가진 모든 행 찾기
                matching_rows = df_clean[df_clean[h_column] == h_value]
                
                # 각 행의 정보 수집
                for idx, row in matching_rows.iterrows():
                    duplicate_analysis.append({
                        'H열_값': h_value,
                        '중복_개수': count,
                        'Cate4': row[cate4_column],
                        'Cate5': row[cate5_column],
                        '상품명': row[product_name_column],
                        '원본_행번호': idx + 2  # Excel에서는 1부터 시작하고 헤더가 있으므로 +2
                    })
        
        # 결과를 DataFrame으로 변환
        result_df = pd.DataFrame(duplicate_analysis)
        
        if len(result_df) > 0:
            # H열 값별로 정렬
            result_df = result_df.sort_values(['H열_값', '원본_행번호'])
            
            print(f"\n중복값 분석 완료!")
            print(f"중복값을 가진 총 행 수: {len(result_df)}")
            
            # 결과 미리보기
            print("\n=== 결과 미리보기 (상위 10개) ===")
            print(result_df.head(10).to_string(index=False))
            
            # 중복값 통계
            print(f"\n=== 중복값 통계 ===")
            duplicate_stats = result_df.groupby('H열_값').agg({
                '중복_개수': 'first',
                'Cate4': lambda x: ', '.join([str(val) for val in x.unique() if pd.notna(val)]),
                'Cate5': lambda x: ', '.join([str(val) for val in x.unique() if pd.notna(val)]),
                '상품명': 'count'
            }).rename(columns={'상품명': '실제_행수'})
            
            print(duplicate_stats.head(10).to_string())
            
            # 결과를 Excel 파일로 저장
            output_filename = 'h열_중복값_분석결과.xlsx'
            
            with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
                # 상세 결과
                result_df.to_excel(writer, sheet_name='상세결과', index=False)
                
                # 통계 요약
                duplicate_stats.to_excel(writer, sheet_name='통계요약')
                
                # 원본 데이터에서 중복값만 필터링
                duplicate_h_values = result_df['H열_값'].unique()
                original_duplicates = df_clean[df_clean[h_column].isin(duplicate_h_values)]
                original_duplicates.to_excel(writer, sheet_name='원본데이터_중복값만', index=False)
            
            print(f"\n결과 파일이 생성되었습니다: {output_filename}")
            print(f"- 상세결과: H열 중복값과 매칭되는 모든 정보")
            print(f"- 통계요약: H열 값별 중복 통계")
            print(f"- 원본데이터_중복값만: 원본 데이터에서 중복값을 가진 행들만 추출")
            
        else:
            print("중복값이 발견되지 않았습니다.")
            
    except Exception as e:
        print(f"오류 발생: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_h_column_duplicates()
