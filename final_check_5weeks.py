import pandas as pd

# 최종 결과 파일 확인
result_file = 'smart_inventory_5weeks_updated_20250728_175554.xlsx'

print(f"🎉 최종 5주차 계산 결과 확인: {result_file}")
print("=" * 60)

# 스마트계산결과 시트 확인
df = pd.read_excel(result_file, sheet_name='스마트계산결과')

print(f"총 행 수: {len(df)}")
print(f"총 컬럼 수: {len(df.columns)}")

# 5주차 계산 결과 확인
print(f"\n📊 5주차 계산 결과:")
print(f"  - 5주차까지 계산된 행 수: {len(df[df['유효주차수'] == 5])}")
print(f"  - 5주사용량이 0보다 큰 행 수: {len(df[df['5주사용량'] > 0])}")

# 유효주차수 분포
print(f"\n📊 유효주차수 분포:")
week_stats = df['유효주차수'].value_counts().sort_index()
for weeks, count in week_stats.items():
    print(f"  - {weeks}주차: {count}개")

# 5주차까지 계산된 샘플 확인
five_week_samples = df[df['유효주차수'] == 5].head(3)
if len(five_week_samples) > 0:
    print(f"\n📋 5주차까지 계산된 샘플 (처음 3개):")
    cols = ['총재고', '1주사용량', '2주사용량', '3주사용량', '4주사용량', '5주사용량', '총사용량', '사용률', '잔여재고']
    print(five_week_samples[cols])

# 요약 통계 확인
print(f"\n📈 요약 통계:")
summary_df = pd.read_excel(result_file, sheet_name='요약통계')
print(summary_df)

print(f"\n✅ 5주 재고 계산이 성공적으로 완료되었습니다!")
print(f"📂 결과 파일: {result_file}")
print(f"🔢 5주차까지 계산된 항목: {len(df[df['유효주차수'] == 5])}개")
print(f"📊 총사용량 증가: 127 → 154 (5주차 계산 포함으로 인한 증가)")
print(f"📈 평균 사용률 증가: 30.9% → 37.6% (5주차 계산 포함으로 인한 증가)")
