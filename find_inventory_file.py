import pandas as pd
import os

# 현재 디렉토리의 모든 Excel 파일 확인
excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and not f.startswith('~')]

print("Excel 파일들의 구조 확인 중...")
print("=" * 50)

inventory_keywords = ['이월재고', '당월입고', '1주', '2주', '3주', '4주', '5주', '재고', '입고량', '기초재고']

for file_name in excel_files:
    print(f"\n📂 {file_name} 분석 중...")
    
    try:
        # 처음 20행을 헤더 없이 읽기
        df = pd.read_excel(file_name, header=None, nrows=20)
        print(f"   크기: {df.shape}")
        
        # 재고 관련 키워드가 있는 행 찾기
        found_keywords = []
        header_row = None
        
        for i in range(min(20, len(df))):
            row_str = ' '.join([str(cell) for cell in df.iloc[i] if pd.notna(cell)])
            
            for keyword in inventory_keywords:
                if keyword in row_str:
                    found_keywords.append((i, keyword))
                    if header_row is None:
                        header_row = i
        
        if found_keywords:
            print(f"   ✅ 재고 관련 키워드 발견:")
            for row_idx, keyword in found_keywords:
                print(f"      행 {row_idx}: '{keyword}'")
            
            # 헤더 행으로 다시 읽어서 컬럼 확인
            if header_row is not None:
                df_with_header = pd.read_excel(file_name, header=header_row)
                print(f"   📋 컬럼 목록 (헤더 행 {header_row}):")
                for i, col in enumerate(df_with_header.columns):
                    if i < 15:  # 처음 15개만 출력
                        print(f"      {i+1}. {col}")
                    elif i == 15:
                        print(f"      ... (총 {len(df_with_header.columns)}개 컬럼)")
                        break
        else:
            print(f"   ❌ 재고 관련 키워드 없음")
            
    except Exception as e:
        print(f"   ❌ 파일 읽기 실패: {e}")

print("\n" + "=" * 50)
print("재고 계산에 적합한 파일을 찾았다면 해당 파일명과 헤더 행 번호를 확인하세요.")
