import pandas as pd

# 결과 파일 확인
result_file = 'smart_inventory_5weeks_updated_20250728_175047.xlsx'

print(f"📊 5주차 계산 결과 확인: {result_file}")
print("=" * 60)

# 스마트계산결과 시트 확인
df = pd.read_excel(result_file, sheet_name='스마트계산결과')

print(f"총 행 수: {len(df)}")
print(f"총 컬럼 수: {len(df.columns)}")

# 5주차 관련 컬럼 확인
week_columns = ['1주사용량', '2주사용량', '3주사용량', '4주사용량', '5주사용량']
available_week_cols = [col for col in week_columns if col in df.columns]

print(f"\n📋 주차별 사용량 컬럼:")
for col in available_week_cols:
    print(f"  - {col}: 존재함")

if '5주사용량' not in available_week_cols:
    print("  ❌ 5주사용량 컬럼이 없습니다!")

# 5주차 데이터가 있는 행 확인
if '5주차' in df.columns:
    print(f"\n📊 5주차 데이터 분석:")
    five_week_data = df[df['5주차'] > 0]
    print(f"  - 5주차 데이터가 있는 행 수: {len(five_week_data)}")
    
    if len(five_week_data) > 0:
        print(f"  - 5주차 데이터 샘플:")
        sample_cols = ['5주차', '5주사용량', '총사용량', '유효주차수', '마지막유효주차']
        available_sample_cols = [col for col in sample_cols if col in df.columns]
        print(five_week_data[available_sample_cols].head(3))
        
        # 5주차까지 계산된 행이 있는지 확인
        five_week_calculated = five_week_data[five_week_data['유효주차수'] == 5]
        print(f"\n  - 5주차까지 계산된 행 수: {len(five_week_calculated)}")
        
        if len(five_week_calculated) > 0:
            print("  ✅ 5주차 계산이 정상적으로 수행되었습니다!")
        else:
            print("  ⚠️ 5주차 데이터는 있지만 5주차까지 계산된 행이 없습니다.")
    else:
        print("  ❌ 5주차 데이터가 있는 행이 없습니다.")
else:
    print("\n❌ 5주차 컬럼이 결과에 없습니다.")

# 유효주차수 분포 확인
print(f"\n📊 유효주차수 분포:")
week_stats = df['유효주차수'].value_counts().sort_index()
for weeks, count in week_stats.items():
    print(f"  - {weeks}주차: {count}개")

# 요약 통계에서 5주 확인
print(f"\n📈 요약 통계에서 5주 확인:")
summary_df = pd.read_excel(result_file, sheet_name='요약통계')
five_week_in_summary = summary_df[summary_df['항목'].str.contains('5주', na=False)]
if len(five_week_in_summary) > 0:
    print("✅ 요약 통계에 5주 항목 포함됨:")
    print(five_week_in_summary)
else:
    print("❌ 요약 통계에 5주 항목이 없습니다.")
