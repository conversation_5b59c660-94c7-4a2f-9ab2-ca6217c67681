import pandas as pd
import sys
import os

def clean_whitespace(df, columns):
    """
    지정된 컬럼들의 공백을 제거하고 숫자 형태를 정규화합니다.
    """
    for col in columns:
        if col in df.columns:
            # 숫자를 문자열로 변환하면서 소수점 제거
            df[col] = df[col].astype(str).str.replace('.0', '', regex=False).str.strip()
    return df

def match_excel_files(file_a_path, file_b_path, output_path=None):
    """
    두 Excel 파일의 externalskuid와 vendoritemid 컬럼을 매칭하여 결과를 출력합니다.
    
    Args:
        file_a_path (str): A.xlsx 파일 경로
        file_b_path (str): B.xlsx 파일 경로
        output_path (str, optional): 결과를 저장할 Excel 파일 경로
    """
    
    try:
        # Excel 파일 읽기 - A.xlsx는 첫 번째 행이 헤더
        print(f"파일 읽는 중: {file_a_path}")
        df_a = pd.read_excel(file_a_path, header=0)  # 첫 번째 행을 헤더로 사용

        # B.xlsx는 두 번째 행이 헤더 (첫 번째 행은 빈 행)
        print(f"파일 읽는 중: {file_b_path}")
        df_b = pd.read_excel(file_b_path, header=1)  # 두 번째 행을 헤더로 사용
        
        print(f"\nA.xlsx 컬럼: {list(df_a.columns)}")
        print(f"B.xlsx 컬럼: {list(df_b.columns)}")
        
        # 컬럼명 정규화 (소문자로 변환하고 공백 제거)
        df_a.columns = [str(col).lower().strip() for col in df_a.columns]
        df_b.columns = [str(col).lower().strip() for col in df_b.columns]
        
        print(f"\n정규화된 A.xlsx 컬럼: {list(df_a.columns)}")
        print(f"정규화된 B.xlsx 컬럼: {list(df_b.columns)}")
        
        # 필요한 컬럼 확인
        required_columns = ['externalskuid', 'vendoritemid']
        
        # 필요한 컬럼이 있는지 확인
        missing_cols_a = [col for col in required_columns if col not in df_a.columns]
        missing_cols_b = [col for col in required_columns if col not in df_b.columns]
        
        if missing_cols_a:
            print(f"경고: A.xlsx에서 누락된 컬럼: {missing_cols_a}")
            print(f"사용 가능한 컬럼: {list(df_a.columns)}")
        if missing_cols_b:
            print(f"경고: B.xlsx에서 누락된 컬럼: {missing_cols_b}")
            print(f"사용 가능한 컬럼: {list(df_b.columns)}")
        
        # 누락된 컬럼이 있으면 종료
        if missing_cols_a or missing_cols_b:
            return None
        
        # 공백 제거
        print("\n공백 제거 중...")
        df_a = clean_whitespace(df_a, required_columns)
        df_b = clean_whitespace(df_b, required_columns)
        
        # 매칭을 위한 키 생성 (externalskuid + vendoritemid)
        df_a['match_key'] = df_a['externalskuid'].astype(str) + '|' + df_a['vendoritemid'].astype(str)
        df_b['match_key'] = df_b['externalskuid'].astype(str) + '|' + df_b['vendoritemid'].astype(str)
        
        # 매칭되는 데이터 찾기
        print("\n매칭되는 데이터 찾는 중...")
        
        # Inner join으로 매칭되는 데이터만 가져오기
        merged_df = pd.merge(
            df_a, 
            df_b, 
            on='match_key', 
            how='inner',
            suffixes=('_A', '_B')
        )
        
        # match_key 컬럼 제거
        merged_df = merged_df.drop('match_key', axis=1)
        
        print(f"\n매칭 결과:")
        print(f"A.xlsx 총 행 수: {len(df_a)}")
        print(f"B.xlsx 총 행 수: {len(df_b)}")
        print(f"매칭된 행 수: {len(merged_df)}")
        
        if len(merged_df) > 0:
            print(f"\n매칭된 데이터 미리보기 (처음 5행):")
            print(merged_df.head().to_string())
            
            # 결과를 Excel 파일로 저장
            if output_path:
                merged_df.to_excel(output_path, index=False)
                print(f"\n결과가 {output_path}에 저장되었습니다.")
            else:
                # 기본 출력 파일명
                output_path = "matched_results.xlsx"
                merged_df.to_excel(output_path, index=False)
                print(f"\n결과가 {output_path}에 저장되었습니다.")
            
            # CSV로도 저장 (선택사항)
            csv_path = output_path.replace('.xlsx', '.csv')
            merged_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"CSV 형태로도 {csv_path}에 저장되었습니다.")
            
        else:
            print("\n매칭되는 데이터가 없습니다.")
            
        return merged_df
        
    except FileNotFoundError as e:
        print(f"파일을 찾을 수 없습니다: {e}")
        return None
    except Exception as e:
        print(f"오류가 발생했습니다: {e}")
        return None

def main():
    """
    메인 함수
    """
    # 파일 경로 설정
    file_a = "A.xlsx"
    file_b = "B.xlsx"
    output_file = "matched_results.xlsx"
    
    # 파일 존재 확인
    if not os.path.exists(file_a):
        print(f"오류: {file_a} 파일이 존재하지 않습니다.")
        return
    
    if not os.path.exists(file_b):
        print(f"오류: {file_b} 파일이 존재하지 않습니다.")
        return
    
    print("Excel 파일 매칭 프로그램을 시작합니다...")
    print("=" * 50)
    
    # 매칭 실행
    result = match_excel_files(file_a, file_b, output_file)
    
    if result is not None:
        print("\n" + "=" * 50)
        print("프로그램이 성공적으로 완료되었습니다!")
    else:
        print("\n" + "=" * 50)
        print("프로그램 실행 중 오류가 발생했습니다.")

if __name__ == "__main__":
    main()
