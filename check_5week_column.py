import pandas as pd

# inventory.xlsx 파일의 5주차 컬럼 확인
print("inventory.xlsx 파일의 5주차 컬럼 확인 중...")

df = pd.read_excel('inventory.xlsx', header=0)

print(f"총 컬럼 수: {len(df.columns)}")
print("\n모든 컬럼 목록:")
for i, col in enumerate(df.columns):
    print(f"{i+1:2d}. '{col}'")

print("\n5주 관련 컬럼 검색:")
five_week_keywords = ['5주', '5차', '5week', '5Week', '5WEEK']

found_5week_cols = []
for col in df.columns:
    col_str = str(col).strip()
    for keyword in five_week_keywords:
        if keyword in col_str:
            found_5week_cols.append(col)
            print(f"✅ 발견: '{col}' (키워드: '{keyword}')")

if not found_5week_cols:
    print("❌ 5주 관련 컬럼을 찾을 수 없습니다.")
    
    # 숫자가 포함된 컬럼들 확인
    print("\n숫자가 포함된 컬럼들:")
    for col in df.columns:
        col_str = str(col).strip()
        if any(char.isdigit() for char in col_str):
            print(f"  - '{col}'")

# 실제 데이터 확인
print(f"\n처음 5행의 데이터:")
print(df.head())

# 주차 관련 컬럼들 확인
week_keywords = ['1주', '2주', '3주', '4주', '5주', '주차']
print(f"\n주차 관련 컬럼들:")
for col in df.columns:
    col_str = str(col).strip().lower()
    for keyword in week_keywords:
        if keyword in col_str:
            print(f"  - '{col}'")
            break
