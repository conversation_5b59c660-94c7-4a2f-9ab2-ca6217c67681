import pandas as pd

# 파일 구조 확인
print("1.xlsx 파일 구조 확인 중...")

# 처음 10행을 헤더 없이 읽기
df = pd.read_excel('1.xlsx', header=None, nrows=10)

print(f"파일 크기: {df.shape}")
print("\n처음 10행:")
for i in range(min(10, len(df))):
    row_data = df.iloc[i].tolist()[:15]  # 처음 15개 컬럼만
    print(f"행 {i}: {row_data}")

# 재고 관련 키워드가 있는 행 찾기
print("\n재고 관련 키워드 검색:")
keywords = ['이월재고', '당월입고', '1주', '2주', '3주', '4주', '5주', '재고', '입고']

for i in range(min(20, len(df))):
    row_str = ' '.join([str(cell) for cell in df.iloc[i] if pd.notna(cell)])
    for keyword in keywords:
        if keyword in row_str:
            print(f"행 {i}에서 '{keyword}' 발견: {row_str[:100]}...")
            break
