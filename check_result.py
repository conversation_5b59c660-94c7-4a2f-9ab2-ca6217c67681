import pandas as pd

# 결과 파일 확인
result_file = 'smart_inventory_5weeks_20250728_173008.xlsx'

print(f"📊 결과 파일 확인: {result_file}")
print("=" * 50)

# 스마트계산결과 시트 확인
df = pd.read_excel(result_file, sheet_name='스마트계산결과')

print(f"총 행 수: {len(df)}")
print(f"총 컬럼 수: {len(df.columns)}")

print("\n📋 컬럼 목록:")
for i, col in enumerate(df.columns):
    print(f"{i+1:2d}. {col}")

print("\n📊 계산 결과 샘플 (처음 3행):")
result_cols = ['총재고', '1주사용량', '2주사용량', '3주사용량', '4주사용량', '5주사용량', '총사용량', '사용률', '잔여재고']
available_cols = [col for col in result_cols if col in df.columns]

print("사용 가능한 결과 컬럼:", available_cols)
print(df[available_cols].head(3))

# 요약 통계 확인
print("\n📈 요약 통계:")
summary_df = pd.read_excel(result_file, sheet_name='요약통계')
print(summary_df)

print("\n✅ 5주 재고 계산이 완료되었습니다!")
if '5주사용량' in df.columns:
    print("✅ 5주 계산 포함됨")
else:
    print("⚠️ 5주 컬럼이 없어서 4주까지만 계산됨")
