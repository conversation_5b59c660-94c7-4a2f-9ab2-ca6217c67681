from smart_inventory_calculator import smart_inventory_calculator
from datetime import datetime

# 결과 파일명 생성
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
result_file = f'smart_inventory_5weeks_{timestamp}.xlsx'

print(f"📊 5주 재고 계산 실행 중...")
print(f"📂 입력 파일: inventory.xlsx")
print(f"📂 결과 파일: {result_file}")
print()

# 스마트 재고 계산 실행
smart_inventory_calculator(
    file_path="inventory.xlsx",
    result_file=result_file,
    sheet_name=None,  # 자동 탐지
    header_row=0  # 헤더 행 번호
)
